package com.rs.module.ihc.service.dch.handler;

import com.rs.module.base.service.dch.context.DataChangeEventContext;
import com.rs.module.base.service.dch.enums.DataChangeEventTypeEnum;
import com.rs.module.base.service.dch.handler.AbstractDataChangeEventHandler;
import com.rs.module.base.service.dch.handler.DataChangeEventHandlerResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 半年体检
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
public class BntjHandler extends AbstractDataChangeEventHandler {

    @Override
    public String getSupportedBusinessType() {
        return BntjHandler.class.getSimpleName();
    }

    @Override
    public int getPriority() {
        return 1;
    }

    @Override
    protected List<String> getSupportedTableNames() {
        return Arrays.asList(
                "acp_pm_prisoner_kss_in"
        );
    }

    @Override
    protected Set<DataChangeEventTypeEnum> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(
                DataChangeEventTypeEnum.INSERT
        ));
    }

    @Override
    protected DataChangeEventHandlerResult doHandle(DataChangeEventContext context) {
        try {
            DataChangeEventHandlerResult.success();
        } catch (Exception e) {
            log.error("处理监管人员数据变更异常", e);
            return DataChangeEventHandlerResult.failure("处理异常: " + e.getMessage());
        }
        return DataChangeEventHandlerResult.success();
    }


    /**
     * 处理监管人员基础信息新增
     */
    private DataChangeEventHandlerResult handlePrisonerInsert(DataChangeEventContext context) {
        // 示例：监管人员新增时的业务逻辑
        // 1. 发送通知
        // 2. 更新统计信息
        // 3. 同步到其他系统

        String prisonerName = (String) getNewValue(context, "xm");
        String prisonerCode = (String) getNewValue(context, "jgrybm");

        log.info("监管人员新增: 姓名={}, 编码={}", prisonerName, prisonerCode);

        // 这里可以添加具体的业务逻辑
        // 例如：发送消息、更新缓存、同步数据等

        return DataChangeEventHandlerResult.success("监管人员新增处理完成");
    }


    @Override
    public void onSuccess(DataChangeEventContext context, DataChangeEventHandlerResult result) {
        log.debug("监管人员数据变更处理成功: 表名={}, 主键ID={}, 耗时={}ms",
                context.getTableName(), context.getPrimaryKeyId(), result.getProcessingTime());
    }

    @Override
    public void onFailure(DataChangeEventContext context, Exception exception) {
        log.error("监管人员数据变更处理失败: 表名={}, 主键ID={}",
                context.getTableName(), context.getPrimaryKeyId(), exception);
    }
}
